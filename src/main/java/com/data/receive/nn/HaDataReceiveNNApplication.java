package com.data.receive.nn;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.PropertySource;

@MapperScan("com.data.receive.nn.dao")
@PropertySource(value = {"classpath:config/config.properties"}, encoding = "UTF-8")
@SpringBootApplication(scanBasePackages = {"com.data.receive.nn", "com.air.security.sodb"})
public class HaDataReceiveNNApplication {

    public static void main(String[] args) {
        SpringApplication.run(HaDataReceiveNNApplication.class, args);
    }

}
