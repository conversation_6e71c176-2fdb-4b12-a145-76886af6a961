package com.data.receive.nn.controller;

import com.data.receive.nn.service.JWTService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 报警数据控制器
 * 使用token调用第三方接口获取报警数据
 * <AUTHOR>
 * @date 2025/8/22
 */
@RestController
@RequestMapping("/alarm")
public class AlarmDataController {
    
    private static final Logger log = LoggerFactory.getLogger(AlarmDataController.class);
    
    @Resource
    private JWTService jwtService;
    
    /**
     * 获取报警数据接口
     * GET /alarm/data?token=xxx
     * 或者
     * GET /alarm/data
     * Authorization: Bearer xxx
     * 
     * @return 报警数据JSON
     */
    @GetMapping("/data")
    public ResponseEntity<String> getAlarmData() {
        
        log.info("Received alarm data request");
        
        try {


            log.info("Using token to fetch alarm data");
            
            // 调用JWT服务获取报警数据
            String alarmData = jwtService.getAlarmData();
            
            if (alarmData != null) {
                log.info("Successfully retrieved alarm data");
                return ResponseEntity.ok(alarmData);
            } else {
                log.warn("Failed to retrieve alarm data");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body("{\"error\": \"Failed to fetch alarm data\"}");
            }
            
        } catch (Exception e) {
            log.error("Error retrieving alarm data", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("{\"error\": \"Internal server error\"}");
        }
    }
}
