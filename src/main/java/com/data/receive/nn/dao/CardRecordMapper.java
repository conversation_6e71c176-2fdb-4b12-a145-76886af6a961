package com.data.receive.nn.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.data.receive.nn.domain.entity.CardRecordInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 门禁刷卡记录Mapper
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Mapper
@DS("mysql")
public interface CardRecordMapper extends BaseMapper<CardRecordInfo> {

    /**
     * 获取最大的交易历史ID
     *
     * @return 最大的交易历史ID
     */
    @Select("SELECT IFNULL(MAX(trn_his_id), 0) FROM sodb_biz_card_record_info")
    Long getMaxTrnHisId();

    /**
     * 批量插入门禁刷卡记录
     *
     * @param cardRecords 门禁刷卡记录列表
     * @return 插入的记录数
     */
    int batchInsertCardRecords(@Param("cardRecords") List<CardRecordInfo> cardRecords);
}
