package com.data.receive.nn.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.data.receive.nn.domain.entity.CardStaffInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 员工卡信息Mapper
 * 
 * <AUTHOR>
 * @date 2025/09/01
 */
@Mapper
@DS("mysql")
public interface CardStaffMapper extends BaseMapper<CardStaffInfo> {

    /**
     * 根据卡持有者ID查询员工信息
     * 
     * @param cardholderId 卡持有者ID
     * @return 员工卡信息
     */
    @Select("SELECT USER_NAME as userName, USER_ID_NUM as userIdNum, CARD_PHOTO_URL as cardPhotoUrl " +
            "FROM sodb_card_staff_info WHERE USER_ID = #{cardholderId}")
    CardStaffInfo getStaffInfoByCardholderId(@Param("cardholderId") String cardholderId);
}
