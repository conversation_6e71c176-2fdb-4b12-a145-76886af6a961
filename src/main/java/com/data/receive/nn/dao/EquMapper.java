package com.data.receive.nn.dao;

import com.data.receive.nn.domain.vo.AlarmInfoV3;
import com.data.receive.nn.domain.vo.EquInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/22
 */
public interface EquMapper {
    /**
     * 获取海康设备
     * @return
     */
    List<EquInfoVO> getList(@Param("equFacturer") String equFacturer, @Param("equClassCode")String equClassCode);
    /**
     * 插入报警信息
     * @param alarmInfoV3
     */
    void insert(@Param("alarmInfoV3")AlarmInfoV3 alarmInfoV3);
}
