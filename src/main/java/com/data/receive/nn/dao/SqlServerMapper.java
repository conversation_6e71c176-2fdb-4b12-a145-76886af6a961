package com.data.receive.nn.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.data.receive.nn.domain.dto.TrnHisDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SQL Server数据访问Mapper
 * 
 * <AUTHOR>
 * @date 2025/09/01
 */
@Mapper
@DS("sqlserver")
public interface SqlServerMapper {

    /**
     * 查询TrnHis和CrdHis表的关联数据
     * 
     * @param maxTrnHisId 最大交易历史ID
     * @return 交易历史数据列表
     */
    List<TrnHisDTO> getTrnHisWithCrdHis(@Param("maxTrnHisId") Long maxTrnHisId);
}
