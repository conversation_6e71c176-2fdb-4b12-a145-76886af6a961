package com.data.receive.nn.domain.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 门禁刷卡信息DTO
 * 用于整合SQL Server和MySQL的查询结果
 * 
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class AccessCardRecordDTO {

    /**
     * 交易历史ID
     */
    private Long trnHisId;

    /**
     * 交易时间
     */
    private LocalDateTime transactionDateTime;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 卡持有者ID
     */
    private String cardholderId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户身份证号
     */
    private String userIdNum;

    /**
     * 卡片照片URL
     */
    private String cardPhotoUrl;
}
