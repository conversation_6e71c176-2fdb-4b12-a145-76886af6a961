package com.data.receive.nn.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 员工卡信息实体类
 * 对应表：sodb_card_staff_info
 * 
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
@TableName("sodb_card_staff_info")
public class CardStaffInfo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 卡持有者ID
     */
    private String cardholderId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户身份证号
     */
    private String userIdNum;

    /**
     * 卡片照片URL
     */
    private String cardPhotoUrl;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
