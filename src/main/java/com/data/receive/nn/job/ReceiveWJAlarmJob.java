package com.data.receive.nn.job;

import com.air.security.sodb.core.util.HaLog;
import com.air.security.sodb.core.util.HaUtil;
import com.air.security.sodb.habase.util.BeanUtils;
import com.data.receive.nn.service.AlarmService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2025/8/22
 */
public class ReceiveWJAlarmJob implements Job{

    private static final Logger log = LoggerFactory.getLogger(ReceiveWJAlarmJob.class);

    private final AlarmService alarmService =
            (AlarmService) BeanUtils.getBean("alarmService");

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        String instr = "接入围界报警数据";
        HaLog.info(log, HaUtil.formatMsg(HaUtil.MSG_START, instr));
        try {
            alarmService.receiveAlarmAndSend();
        } catch (Exception e) {
            HaLog.error(log, e.getMessage(), e);
        } finally {
            HaLog.info(log, HaUtil.formatMsg(HaUtil.MSG_END, instr));
        }
    }
}
