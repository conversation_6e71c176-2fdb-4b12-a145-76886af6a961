package com.data.receive.nn.mq;

import com.air.security.sodb.habase.constant.HaConstant;
import com.data.receive.nn.service.AlarmService;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * @author: CuiZ
 * @date: 2021/1/12 17:29
 * @description: GIS+BIM 平台门禁报警监听
 */
@Component
@ConditionalOnProperty(name="data.msg.type", havingValue = "rocketmq")
@RocketMQMessageListener(consumerGroup = "consumer-group_alarm",topic = "noused")
public class AlarmMqConsumer implements RocketMQListener<MessageExt> {

    @Autowired
    private AlarmService alarmService;
    /**
     * 出入口消息处理
     * @param messageExt
     */
    @Override
    public void onMessage(MessageExt messageExt) {
        String message = new String(messageExt.getBody(), HaConstant.GLOBAL_CHARSET);
        alarmService.receiveMessage(message);
    }
}
