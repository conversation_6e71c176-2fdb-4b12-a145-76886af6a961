package com.data.receive.nn.service;

import com.data.receive.nn.domain.dto.AuthRequest;
import com.data.receive.nn.domain.vo.AuthResponse;

/**
 * <AUTHOR>
 * @date 2025/8/22
 */
public interface JWTService {

    /**
     * 用户认证方法
     * 用户使用用户名密码来请求服务器，返回JWT token
     * @param authRequest 认证请求，包含用户名和密码
     * @return 认证响应，包含access_token
     */
    AuthResponse authenticate(AuthRequest authRequest);

    /**
     * 使用token调用第三方接口获取报警数据
     * @return 报警数据JSON字符串
     */
    String getAlarmData();
}
