package com.data.receive.nn.service.impl;

import com.air.security.sodb.core.util.UuidUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.data.receive.nn.domain.dto.AlarmDataDTO;
import com.data.receive.nn.domain.dto.MJAlarmDTO;
import com.data.receive.nn.domain.vo.Meta;
import com.data.receive.nn.domain.vo.WJAlarmResponse;
import com.data.receive.nn.service.AlarmService;
import com.data.receive.nn.service.JWTService;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/7/7
 */
@Service("alarmService")
public class AlarmServiceImpl implements AlarmService {
    private static final Logger log = LoggerFactory.getLogger(AlarmServiceImpl.class);
    @Resource
    private DefaultMQProducer producer;
    @Value("${alarm.topic}")
    private String topic;
    @Value("${alarm.forward.topic}")
    private String forwardTopic;
    @Value("${meta.sender}")
    private String sender;
    @Value("${meta.receiver}")
    private String receiver;
    @Value("${meta.msg.type}")
    private String msgType;
    @Value("${meta.event.type}")
    private String eventType;

    @Value("${alarm.class.name}")
    private String alarmClassName;
    @Value("${alarm.class.code}")
    private String alarmClassCode;
    @Value("${alarm.name.code}")
    private String alarmNameCode;
    @Value("${alarm.name}")
    private String alarmName;
    @Value("${alarm.state.code}")
    private String alarmStateCode;
    @Value("${alarm.state.name}")
    private String alarmStateName;
    @Value("${airport.iata}")
    private String airportIata;
    @Value("${airport.name}")
    private String airportName;
    @Value("${alarm.mj.class.name}")
    private String mjAlarmClassName;
    @Value("${alarm.mj.class.code}")
    private String mjAlarmClassCode;
    @Value("${alarm.mj.map}")
    private String mjAlarmMap;

    @Resource
    private JWTService jwtService;

    private <T> void sendMessage(T t,Meta meta) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("meta",meta);
        jsonObject.put("body",t);

        try {
            log.info("发送内容》》》"+jsonObject.toJSONString());
            Message msg = new Message(topic, jsonObject.toJSONString().getBytes("utf-8"));
            SendResult sendResult = producer.send(msg);
            log.info("发送成功状态为》》》"+sendResult.getSendStatus()+",MsgId为》》》"+sendResult.getMsgId());
        }catch (Exception e){
            log.error("发送失败：",e);
        }
    }

    private <T> void forwardMessage(T t,Meta meta) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("meta",meta);
        jsonObject.put("body",t);

        try {
            log.info("转发数据：发送内容》》》"+jsonObject.toJSONString());
            Message msg = new Message(forwardTopic, jsonObject.toJSONString().getBytes("utf-8"));
            SendResult sendResult = producer.send(msg);
            log.info("转发数据：发送成功状态为》》》"+sendResult.getSendStatus()+",MsgId为》》》"+sendResult.getMsgId());
        }catch (Exception e){
            log.error("发送失败：",e);
        }
    }

    @Override
    public void receiveAlarmAndSend() {
        String alarmData = jwtService.getAlarmData();
        log.info("获取报警数据:"+alarmData);
        if (Objects.isNull(alarmData)){
            return;
        }
        List<WJAlarmResponse> responseList = JSONArray.parseArray(alarmData, WJAlarmResponse.class);
        responseList.forEach(item->{
            String equCode = "SEG_"+item.getSystem()+"_"+item.getSegment();
            String sequence = equCode+"_"+item.getOffset()+"_"+item.getTimeStamp();
            AlarmDataDTO dto = new AlarmDataDTO();
            dto.setAlarmEquCode(equCode);
            dto.setAlarmName(alarmName);
            dto.setAlarmNameCode(alarmNameCode);
            dto.setAlarmClassName(alarmClassName);
            dto.setAlarmClassCode(alarmClassCode);
            dto.setAirportIata(airportIata);
            dto.setAirportName(airportName);
            dto.setAlarmTime(item.getTimeStamp());
            dto.setAlarmStateCode(alarmStateCode);
            dto.setAlarmStateName(alarmStateName);
            dto.setAlarmDescribe(String.valueOf(item.getOffset()));
            Meta meta = getMeta(sequence, sender, receiver, msgType, eventType);
            sendMessage(dto, meta);
        });
    }

    @Override
    public void receiveMessage(String message) {
        log.info("接收到GIS+BIM 平台门禁报警消息:"+message);
        try {
            MJAlarmDTO alarmMessage = JSONObject.parseObject(message, MJAlarmDTO.class);
            log.info("解析后的报警消息: {}", alarmMessage);

            // 处理报警消息的业务逻辑
            processDeviceAlarm(alarmMessage);

        } catch (Exception e) {
            log.error("解析设备报警消息失败: {}", message, e);
        }
    }

    private void processDeviceAlarm(MJAlarmDTO alarmMessage) {
        if (Objects.isNull(alarmMessage)
                || Objects.isNull(alarmMessage.getDeviceID())
                || StringUtils.isBlank(alarmMessage.getTrnType())
                || StringUtils.isBlank(alarmMessage.getTrnTime())
        ){
            log.warn("报警消息不完整: {}", alarmMessage);
            return;
        }
        String alarmName = "";
        String alarmNameCode="";
        JSONArray mjAlarmMapArray = JSONArray.parseArray(mjAlarmMap);
        for (Object item : mjAlarmMapArray) {
            JSONObject jsonObject = (JSONObject) item;
            if (jsonObject.getInteger("TrnTypeID").equals(alarmMessage.getTrnTypeID())){
                alarmName = jsonObject.getString("alarmName");
                alarmNameCode = jsonObject.getString("alarmNameCode");
            }
        }

        if (StringUtils.isBlank(alarmName) || StringUtils.isBlank(alarmNameCode)){
            log.warn("无法匹配报警编码: {}", alarmMessage);
            return;
        }

        AlarmDataDTO dto = new AlarmDataDTO();
        dto.setAlarmEquCode(String.valueOf(alarmMessage.getDeviceID()));
        dto.setAlarmName(alarmName);
        dto.setAlarmNameCode(alarmNameCode);
        dto.setAlarmClassName(mjAlarmClassName);
        dto.setAlarmClassCode(mjAlarmClassCode);
        dto.setAirportIata(airportIata);
        dto.setAirportName(airportName);
        dto.setAlarmTime(alarmMessage.getTrnTime());
        dto.setAlarmStateCode(alarmStateCode);
        dto.setAlarmStateName(alarmStateName);
        Meta meta = getMeta(UuidUtil.getUuid32(), sender, receiver, msgType, eventType);
        sendMessage(dto, meta);
        forwardMessage(dto,meta);
    }

    private Meta getMeta(String sequence,String sender,String receiver,String msgType,String eventType){
        Meta meta = new Meta();
        meta.setSender(sender);
        meta.setReceiver(receiver);
        meta.setSequence(sequence);
        meta.setRecvSequence("");
        LocalDateTime now = LocalDateTime.now();
        String formattedTime = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        meta.setSendTime(formattedTime);
        meta.setRecvTime("");
        meta.setForwardTime(formattedTime);
        meta.setMsgType(msgType);
        meta.setEventType(eventType);
        return meta;
    }

}
