package com.data.receive.nn.service.impl;

import com.data.receive.nn.dao.CardRecordMapper;
import com.data.receive.nn.dao.CardStaffMapper;
import com.data.receive.nn.dao.SqlServerMapper;
import com.data.receive.nn.domain.dto.AccessCardRecordDTO;
import com.data.receive.nn.domain.dto.TrnHisDTO;
import com.data.receive.nn.domain.entity.CardRecordInfo;
import com.data.receive.nn.domain.entity.CardStaffInfo;
import com.data.receive.nn.service.CardSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 门禁刷卡信息同步服务实现类
 * 
 * <AUTHOR>
 * @date 2025/09/01
 */
@Slf4j
@Service
public class CardSyncServiceImpl implements CardSyncService {

    @Autowired
    private CardRecordMapper cardRecordMapper;

    @Autowired
    private CardStaffMapper cardStaffMapper;

    @Autowired
    private SqlServerMapper sqlServerMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int syncCardRecords() {
        try {
            // 1. 查询MySQL中sodb_biz_card_record_info表的最大trn_his_id
            Long maxTrnHisId = cardRecordMapper.getMaxTrnHisId();
            log.info("获取到的最大交易历史ID: {}", maxTrnHisId);

            // 2. 查询SQL Server中TrnHis和CrdHis表的关联数据
            List<TrnHisDTO> trnHisList = sqlServerMapper.getTrnHisWithCrdHis(maxTrnHisId);
            if (CollectionUtils.isEmpty(trnHisList)) {
                log.info("没有新的交易记录需要同步");
                return 0;
            }
            log.info("从SQL Server获取到 {} 条新的交易记录", trnHisList.size());

            // 3. 遍历交易记录，获取员工信息并组装完整的门禁刷卡信息
            List<AccessCardRecordDTO> accessCardRecords = new ArrayList<>();
            for (TrnHisDTO trnHis : trnHisList) {
                // 根据CardholderID查询MySQL中的员工信息
                CardStaffInfo staffInfo = cardStaffMapper.getStaffInfoByCardholderId(trnHis.getCardholderId());
                
                AccessCardRecordDTO accessRecord = new AccessCardRecordDTO();
                accessRecord.setTrnHisId(trnHis.getTrnHisId());
                accessRecord.setTransactionDateTime(trnHis.getTransactionDateTime());
                accessRecord.setDeviceId(trnHis.getDeviceId());
                accessRecord.setCardholderId(trnHis.getCardholderId());
                
                if (staffInfo != null) {
                    accessRecord.setUserName(staffInfo.getUserName());
                    accessRecord.setUserIdNum(staffInfo.getUserIdNum());
                    accessRecord.setCardPhotoUrl(staffInfo.getCardPhotoUrl());
                } else {
                    log.warn("未找到CardholderID为 {} 的员工信息", trnHis.getCardholderId());
                }
                
                accessCardRecords.add(accessRecord);
            }

            // 4. 将组装好的数据批量插入到MySQL的sodb_biz_card_record_info表中
            List<CardRecordInfo> cardRecords = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();

            for (AccessCardRecordDTO accessRecord : accessCardRecords) {
                CardRecordInfo cardRecord = new CardRecordInfo();
                cardRecord.setTrnHisId(accessRecord.getTrnHisId());
                cardRecord.setRecordTime(accessRecord.getTransactionDateTime());
                cardRecord.setDeviceCode(accessRecord.getDeviceId());
                cardRecord.setPersonName(accessRecord.getUserName());
                cardRecord.setPaperNumber(accessRecord.getUserIdNum());
                cardRecord.setPersonPic(accessRecord.getCardPhotoUrl());
                cardRecord.setCreateTime(now);
                cardRecord.setUpdateTime(now);

                cardRecords.add(cardRecord);
            }

            // 批量插入
            int insertCount = cardRecordMapper.batchInsertCardRecords(cardRecords);
            log.info("成功同步 {} 条门禁刷卡记录", insertCount);
            return insertCount;

        } catch (Exception e) {
            log.error("同步门禁刷卡记录失败", e);
            throw e;
        }
    }
}
