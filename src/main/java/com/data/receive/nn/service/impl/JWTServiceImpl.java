package com.data.receive.nn.service.impl;

import com.alibaba.fastjson.JSON;
import com.data.receive.nn.domain.dto.AuthRequest;
import com.data.receive.nn.domain.vo.AuthResponse;
import com.data.receive.nn.service.JWTService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2025/8/22
 */
@Service
public class JWTServiceImpl implements JWTService, CommandLineRunner {

    private static final Logger log = LoggerFactory.getLogger(JWTServiceImpl.class);

    // 第三方认证服务地址
    @Value("${auth.server.url:http://localhost:8080}")
    private String authServerUrl;

    // 第三方认证接口路径
    @Value("${auth.server.auth-path:/auth}")
    private String authPath;

    // 第三方报警数据接口路径
    @Value("${auth.server.alarm-path:/v1/cables}")
    private String alarmDataPath;

    @Value("${auth.username}")
    private String username;
    @Value("${auth.password}")
    private String password;

    private String accessToken;
    // HTTP客户端
    private final RestTemplate restTemplate = new RestTemplate();


    @Override
    public AuthResponse authenticate(AuthRequest authRequest) {
        log.info("开始登录认证，用户名: {}", authRequest.getUsername());

        try {
            // 构建完整的认证URL
            String fullAuthUrl = authServerUrl + authPath;
            log.info("构建完整的认证URL: {}", fullAuthUrl);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            String requestBody = JSON.toJSONString(authRequest);
            log.info("构建请求体: {}", requestBody);

            // 创建HTTP请求实体
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

            // 发送POST请求到第三方认证服务
            ResponseEntity<String> response = restTemplate.exchange(
                    fullAuthUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            // 检查响应状态
            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                log.info("认证成功，用户名: {}", authRequest.getUsername());
                log.info("响应体: {}", responseBody);

                // 解析响应获取token
                AuthResponse authResponse = JSON.parseObject(responseBody, AuthResponse.class);

                if (authResponse != null && authResponse.getAccess_token() != null) {
                    // 将token保存到内存缓存中
                    accessToken = authResponse.getAccess_token();
                    log.info("缓存token");

                    return authResponse;
                } else {
                    log.warn("响应内容为空");
                    return null;
                }
            }  else {
                log.warn("认证失败，用户名: {}, 状态码: {}",
                        authRequest.getUsername(), response.getStatusCode());
                return null;
            }

        } catch (Exception e) {
            log.error("认证异常，用户名: {}", authRequest.getUsername(), e);
            return null;
        }
    }

    @Override
    public String getAlarmData() {
        log.info("开始请求报警数据");

        try {
            // 构建完整的报警数据URL
            String fullAlarmUrl = authServerUrl + alarmDataPath;
            log.info("构建完整的请求报警URL: {}", fullAlarmUrl);

            // 设置请求头，包含Authorization Bearer token
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + accessToken);
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建HTTP请求实体
            HttpEntity<String> requestEntity = new HttpEntity<>(headers);

            // 发送GET请求到第三方报警数据服务
            ResponseEntity<String> response = restTemplate.exchange(
                    fullAlarmUrl,
                    HttpMethod.GET,
                    requestEntity,
                    String.class
            );

            // 检查响应状态
            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                log.info("获取报警数据成功");
                log.info("响应体: {}", responseBody);

                return responseBody;
            }else if (response.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                log.warn("token错误，token: {}, 状态码: {}",
                        accessToken, response.getStatusCode());
                authenticate(new AuthRequest(username, password));
                return getAlarmData();
            } else {
                log.warn("获取报警失败, 状态码: {}", response.getStatusCode());
                authenticate(new AuthRequest(username, password));
                return getAlarmData();
            }

        } catch (Exception e) {
            log.error("获取报警数据异常", e);
            authenticate(new AuthRequest(username, password));
            return getAlarmData();
        }
    }

    @Override
    public void run(String... args) throws Exception {
        AuthRequest authRequest = new AuthRequest();
        authRequest.setUsername(username);
        authRequest.setPassword(password);
        authenticate(authRequest);
    }
}