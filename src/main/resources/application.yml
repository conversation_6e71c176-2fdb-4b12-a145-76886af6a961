server:
  port: 8183
  servlet:
    context-path: /
spring:
  application:
    name: sodb-data-receive-nn
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      primary: mysql #设置默认数据库源
      strict: false #严格匹配数据源，默认为false，true未匹配到指定数据库时抛异常，false使用默认数据源
      datasource:
        mysql:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *********************************************************************************************************************************************************
          username: root
          password: 123456
          type: com.alibaba.druid.pool.DruidDataSource
        sqlserver:
          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
          url: ***************************************************************************************************
          username: sa
          password: yourpassword
          type: com.alibaba.druid.pool.DruidDataSource
#        oracle:
#          driver-class-name: oracle.jdbc.OracleDriver
#          url: *******************************************
#          username: sodb_bs
#          password: 123456
#          type: com.alibaba.druid.pool.DruidDataSource

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: flag # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
#showSql
logging:
  level:
    root: INFO
    com.baomidou.mybatisplus: DEBUG
  file:
    name: /server/log/recevie/sodb-data-receive-nn.log

# RocketMQ 配置
rocketmq:
  name-server: ***************:9876
  producer:
    group: basic-group
data:
  msg:
    type: rocketmq
# 第三方认证服务配置
auth:
  server:
    url: http://localhost:8088  # 第三方认证服务地址，请根据实际情况修改IP和PORT
    auth-path: /auth  # 认证接口路径
    alarm-path: /v1/cables # 报警数据接口路径
