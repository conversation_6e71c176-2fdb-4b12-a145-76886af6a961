<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.data.receive.nn.dao.CardRecordMapper">

    <!-- 批量插入门禁刷卡记录 -->
    <insert id="batchInsertCardRecords" parameterType="java.util.List">
        INSERT INTO sodb_biz_card_record_info 
        (trn_his_id, record_time, device_code, person_name, paper_number, person_pic, create_time, update_time)
        VALUES
        <foreach collection="cardRecords" item="item" separator=",">
            (#{item.trnHisId}, #{item.recordTime}, #{item.deviceCode}, #{item.personName}, 
             #{item.paperNumber}, #{item.personPic}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

</mapper>
