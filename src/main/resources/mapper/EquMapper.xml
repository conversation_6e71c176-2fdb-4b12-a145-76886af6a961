<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.data.receive.nn.dao.EquMapper">

    <select id="getList" parameterType="java.util.Map"
            resultType="com.data.receive.nn.domain.vo.EquInfoVO">
        select
        UUID equId,
        EQU_NAME equName,
        EQU_CLASS_CODE equClassCode,
        EQU_CLASS_NAME equClassName,
        EQU_IP equIp,
        EQU_PORT equPort,
        USER_NAME userName,
        USER_PASSWORD userPassword
        from sodb_equ_info
        where EQU_FACTURER like #{equFacturer}
        and EQU_CLASS_CODE = #{equClassCode}
    </select>

    <insert id="insert">
        insert into sodb_alarm_info_v3 (
            UUID,
            ALARM_CLASS_NAME,
            ALARM_CLASS_CODE,
            ALARM_NAME_CODE,
            ALARM_NAME,
            ALARM_LEVEL_CODE,
            ALARM_LEVEL_NAME,
            ALARM_TIME,
            ALARM_STATE_CODE,
            ALARM_STATE_NAME,
            EQU_ID,
            EQU_NAME,
            EQU_CLASS_CODE,
            EQU_CLASS_NAME,
            GIS_X,
            GIS_Y,
            GIS_Z,
            CREATE_DATE)
        values (
            #{alarmInfoV3.uuid},
            #{alarmInfoV3.alarmClassName},
            #{alarmInfoV3.alarmClassCode},
            #{alarmInfoV3.alarmNameCode},
            #{alarmInfoV3.alarmName},
            #{alarmInfoV3.alarmLevelCode},
            #{alarmInfoV3.alarmLevelName},
            #{alarmInfoV3.alarmTime},
            #{alarmInfoV3.alarmStateCode},
            #{alarmInfoV3.alarmStateName},
            #{alarmInfoV3.equId},
            #{alarmInfoV3.equName},
            #{alarmInfoV3.equClassCode},
            #{alarmInfoV3.equClassName},
            #{alarmInfoV3.gisx},
            #{alarmInfoV3.gisy},
            #{alarmInfoV3.gisz},
            #{alarmInfoV3.createDate}
        )
    </insert>
</mapper>