<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.data.receive.nn.dao.SqlServerMapper">

    <!-- 查询TrnHis和CrdHis表的关联数据 -->
    <select id="getTrnHisWithCrdHis" parameterType="java.lang.Long" 
            resultType="com.data.receive.nn.domain.dto.TrnHisDTO">
        SELECT 
            t.TrnHisID as trnHisId,
            t.TransactionDateTime as transactionDateTime,
            t.DeviceID as deviceId,
            c.CardholderID as cardholderId
        FROM TrnHis t
        INNER JOIN CrdHis c ON t.TrnHisID = c.TrnHisID
        WHERE t.TrnHisID > #{maxTrnHisId}
        ORDER BY t.TrnHisID ASC
    </select>

</mapper>
